// ProjectFlow - Project Management Application
class ProjectManager {
    constructor() {
        this.projects = JSON.parse(localStorage.getItem('projects')) || [];
        this.currentProject = null;
        this.currentView = 'dashboard';
        this.currentTaskView = 'kanban';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadDashboard();
        this.updateStats();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchView(e.target.dataset.view);
            });
        });

        // Project view switchers
        document.querySelectorAll('[data-project-view]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchProjectView(e.target.dataset.projectView);
            });
        });

        // Task view switchers
        document.querySelectorAll('[data-task-view]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTaskView(e.target.dataset.taskView);
            });
        });

        // Modal controls
        document.getElementById('newProjectBtn').addEventListener('click', () => {
            this.openModal('newProjectModal');
        });

        document.getElementById('newTaskBtn').addEventListener('click', () => {
            this.openModal('newTaskModal');
        });

        document.querySelectorAll('.close-btn, [data-modal]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (e.target.dataset.modal) {
                    this.closeModal(e.target.dataset.modal);
                } else {
                    this.closeModal(e.target.closest('.modal').id);
                }
            });
        });

        // Forms
        document.getElementById('newProjectForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createProject();
        });

        document.getElementById('newTaskForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createTask();
        });

        // Back button
        document.getElementById('backToProjects').addEventListener('click', () => {
            this.switchView('projects');
        });

        // Search and filters
        document.getElementById('searchProjects').addEventListener('input', (e) => {
            this.filterProjects(e.target.value);
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.filterProjectsByStatus(e.target.value);
        });
    }

    switchView(view) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.view').forEach(v => {
            v.classList.remove('active');
        });

        if (view === 'projects') {
            document.getElementById('projects-view').classList.add('active');
            this.loadProjects();
        } else if (view === 'dashboard') {
            document.getElementById('dashboard-view').classList.add('active');
            this.loadDashboard();
        } else if (view === 'calendar') {
            document.getElementById('calendar-view-main').classList.add('active');
            this.loadCalendar();
        } else if (view === 'analytics') {
            document.getElementById('analytics-view').classList.add('active');
            this.loadAnalytics();
        }

        this.currentView = view;
    }

    switchProjectView(view) {
        document.querySelectorAll('[data-project-view]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-project-view="${view}"]`).classList.add('active');

        const container = document.getElementById('projectsContainer');
        container.className = `projects-container ${view}-view`;
    }

    switchTaskView(view) {
        document.querySelectorAll('[data-task-view]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-task-view="${view}"]`).classList.add('active');

        document.querySelectorAll('.task-view').forEach(v => {
            v.classList.remove('active');
        });

        if (view === 'kanban') {
            document.getElementById('kanban-view').classList.add('active');
            this.loadKanbanBoard();
        } else if (view === 'gantt') {
            document.getElementById('gantt-view').classList.add('active');
            this.loadGanttChart();
        } else if (view === 'calendar') {
            document.getElementById('calendar-view').classList.add('active');
            this.loadTaskCalendar();
        } else if (view === 'list') {
            document.getElementById('list-view').classList.add('active');
            this.loadTaskList();
        }

        this.currentTaskView = view;
    }

    openModal(modalId) {
        document.getElementById(modalId).classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeModal(modalId) {
        document.getElementById(modalId).classList.remove('active');
        document.body.style.overflow = 'auto';
    }

    createProject() {
        const form = document.getElementById('newProjectForm');
        const formData = new FormData(form);
        
        const project = {
            id: this.generateId(),
            name: document.getElementById('projectName').value,
            description: document.getElementById('projectDescription').value,
            startDate: document.getElementById('projectStartDate').value,
            endDate: document.getElementById('projectEndDate').value,
            priority: document.getElementById('projectPriority').value,
            color: document.getElementById('projectColor').value,
            status: 'active',
            progress: 0,
            tasks: [],
            createdAt: new Date().toISOString()
        };

        this.projects.push(project);
        this.saveProjects();
        this.closeModal('newProjectModal');
        form.reset();
        
        if (this.currentView === 'projects') {
            this.loadProjects();
        }
        this.updateStats();
        this.showNotification('Project created successfully!', 'success');
    }

    createTask() {
        if (!this.currentProject) return;

        const task = {
            id: this.generateId(),
            name: document.getElementById('taskName').value,
            description: document.getElementById('taskDescription').value,
            priority: document.getElementById('taskPriority').value,
            status: document.getElementById('taskStatus').value,
            startDate: document.getElementById('taskStartDate').value,
            dueDate: document.getElementById('taskDueDate').value,
            assignee: document.getElementById('taskAssignee').value,
            createdAt: new Date().toISOString(),
            completedAt: null
        };

        this.currentProject.tasks.push(task);
        this.saveProjects();
        this.closeModal('newTaskModal');
        document.getElementById('newTaskForm').reset();
        
        this.loadKanbanBoard();
        this.updateProjectProgress();
        this.updateStats();
        this.showNotification('Task created successfully!', 'success');
    }

    loadDashboard() {
        this.loadRecentProjects();
        this.loadProgressChart();
        this.loadUpcomingDeadlines();
        this.updateStats();
    }

    loadRecentProjects() {
        const container = document.getElementById('recentProjectsList');
        const recentProjects = this.projects
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (recentProjects.length === 0) {
            container.innerHTML = '<p class="empty-state">No projects yet. Create your first project!</p>';
            return;
        }

        container.innerHTML = recentProjects.map(project => `
            <div class="project-card" onclick="projectManager.openProject('${project.id}')">
                <div class="project-card-header">
                    <div class="project-title">${project.name}</div>
                    <span class="status-badge ${project.status}">${project.status}</span>
                </div>
                <div class="project-description">${project.description || 'No description'}</div>
                <div class="project-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${project.progress}%"></div>
                    </div>
                    <span class="progress-text">${project.progress}% Complete</span>
                </div>
                <div class="project-meta">
                    <span>${project.tasks.length} tasks</span>
                    <span>Due: ${this.formatDate(project.endDate)}</span>
                </div>
            </div>
        `).join('');
    }

    loadProjects() {
        const container = document.getElementById('projectsContainer');
        
        if (this.projects.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>No projects yet</h3>
                    <p>Create your first project to get started</p>
                    <button class="btn-primary" onclick="projectManager.openModal('newProjectModal')">
                        <i class="fas fa-plus"></i>
                        Create Project
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = this.projects.map(project => `
            <div class="project-card" onclick="projectManager.openProject('${project.id}')" style="border-left-color: ${project.color}">
                <div class="project-card-header">
                    <div class="project-title">${project.name}</div>
                    <span class="status-badge ${project.status}">${project.status}</span>
                </div>
                <div class="project-description">${project.description || 'No description'}</div>
                <div class="project-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${project.progress}%; background: ${project.color}"></div>
                    </div>
                    <span class="progress-text">${project.progress}% Complete</span>
                </div>
                <div class="project-meta">
                    <span><i class="fas fa-tasks"></i> ${project.tasks.length} tasks</span>
                    <span><i class="fas fa-calendar"></i> ${this.formatDate(project.endDate)}</span>
                </div>
            </div>
        `).join('');
    }

    openProject(projectId) {
        this.currentProject = this.projects.find(p => p.id === projectId);
        if (!this.currentProject) return;

        document.getElementById('projectTitle').textContent = this.currentProject.name;
        document.getElementById('projectStatus').textContent = this.currentProject.status;
        document.getElementById('projectStatus').className = `status-badge ${this.currentProject.status}`;

        document.getElementById('project-detail-view').classList.add('active');
        document.getElementById('projects-view').classList.remove('active');
        
        this.loadKanbanBoard();
    }

    loadKanbanBoard() {
        if (!this.currentProject) return;

        const columns = {
            'todo': document.getElementById('todoColumn'),
            'in-progress': document.getElementById('inProgressColumn'),
            'review': document.getElementById('reviewColumn'),
            'done': document.getElementById('doneColumn')
        };

        // Clear columns
        Object.values(columns).forEach(column => column.innerHTML = '');

        // Group tasks by status
        const tasksByStatus = this.currentProject.tasks.reduce((acc, task) => {
            if (!acc[task.status]) acc[task.status] = [];
            acc[task.status].push(task);
            return acc;
        }, {});

        // Populate columns
        Object.entries(tasksByStatus).forEach(([status, tasks]) => {
            if (columns[status]) {
                columns[status].innerHTML = tasks.map(task => this.createTaskCard(task)).join('');
            }
        });

        // Update task counts
        Object.entries(columns).forEach(([status, column]) => {
            const count = tasksByStatus[status]?.length || 0;
            const header = column.closest('.kanban-column').querySelector('.task-count');
            header.textContent = count;
        });

        this.setupDragAndDrop();
    }

    createTaskCard(task) {
        const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'done';
        
        return `
            <div class="task-card" draggable="true" data-task-id="${task.id}" ${isOverdue ? 'style="border-left-color: #e74c3c"' : ''}>
                <div class="task-title">${task.name}</div>
                <div class="task-description">${task.description || 'No description'}</div>
                <div class="task-meta">
                    <span class="priority-badge ${task.priority}">${task.priority}</span>
                    ${task.dueDate ? `<span class="due-date ${isOverdue ? 'overdue' : ''}">${this.formatDate(task.dueDate)}</span>` : ''}
                </div>
                ${task.assignee ? `<div class="task-assignee"><i class="fas fa-user"></i> ${task.assignee}</div>` : ''}
            </div>
        `;
    }

    setupDragAndDrop() {
        const taskCards = document.querySelectorAll('.task-card');
        const columns = document.querySelectorAll('.column-content');

        taskCards.forEach(card => {
            card.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', e.target.dataset.taskId);
                e.target.classList.add('dragging');
            });

            card.addEventListener('dragend', (e) => {
                e.target.classList.remove('dragging');
            });
        });

        columns.forEach(column => {
            column.addEventListener('dragover', (e) => {
                e.preventDefault();
                column.classList.add('drop-zone');
            });

            column.addEventListener('dragleave', (e) => {
                column.classList.remove('drop-zone');
            });

            column.addEventListener('drop', (e) => {
                e.preventDefault();
                column.classList.remove('drop-zone');
                
                const taskId = e.dataTransfer.getData('text/plain');
                const newStatus = column.closest('.kanban-column').dataset.status;
                
                this.updateTaskStatus(taskId, newStatus);
            });
        });
    }

    updateTaskStatus(taskId, newStatus) {
        const task = this.currentProject.tasks.find(t => t.id === taskId);
        if (!task) return;

        task.status = newStatus;
        if (newStatus === 'done' && !task.completedAt) {
            task.completedAt = new Date().toISOString();
        } else if (newStatus !== 'done') {
            task.completedAt = null;
        }

        this.saveProjects();
        this.loadKanbanBoard();
        this.updateProjectProgress();
        this.updateStats();
    }

    updateProjectProgress() {
        if (!this.currentProject) return;

        const totalTasks = this.currentProject.tasks.length;
        const completedTasks = this.currentProject.tasks.filter(t => t.status === 'done').length;
        
        this.currentProject.progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
        
        if (this.currentProject.progress === 100 && this.currentProject.status === 'active') {
            this.currentProject.status = 'completed';
        }

        this.saveProjects();
    }

    updateStats() {
        const totalProjects = this.projects.length;
        const totalTasks = this.projects.reduce((sum, p) => sum + p.tasks.length, 0);
        const completedTasks = this.projects.reduce((sum, p) => 
            sum + p.tasks.filter(t => t.status === 'done').length, 0);
        const overdueTasks = this.projects.reduce((sum, p) => 
            sum + p.tasks.filter(t => 
                t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'done'
            ).length, 0);

        document.getElementById('totalProjects').textContent = totalProjects;
        document.getElementById('totalTasks').textContent = totalTasks;
        document.getElementById('completedTasks').textContent = completedTasks;
        document.getElementById('overdueTasks').textContent = overdueTasks;
    }

    loadProgressChart() {
        const container = document.getElementById('progressChart');
        const projects = this.projects.slice(0, 5); // Show top 5 projects
        
        if (projects.length === 0) {
            container.innerHTML = '<p class="empty-state">No projects to display</p>';
            return;
        }

        // Create SVG progress chart
        const svg = this.createProgressSVG(projects);
        container.innerHTML = svg;
    }

    createProgressSVG(projects) {
        const width = 400;
        const height = 200;
        const barHeight = 25;
        const spacing = 35;
        
        let svg = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">`;
        
        projects.forEach((project, index) => {
            const y = index * spacing + 10;
            const progressWidth = (project.progress / 100) * 300;
            
            svg += `
                <rect x="10" y="${y}" width="300" height="${barHeight}" fill="#ecf0f1" rx="12"/>
                <rect x="10" y="${y}" width="${progressWidth}" height="${barHeight}" fill="${project.color}" rx="12"/>
                <text x="320" y="${y + 17}" font-size="12" fill="#2c3e50">${project.name} (${project.progress}%)</text>
            `;
        });
        
        svg += '</svg>';
        return svg;
    }

    loadUpcomingDeadlines() {
        const container = document.getElementById('deadlinesList');
        const allTasks = this.projects.flatMap(p => 
            p.tasks.map(t => ({...t, projectName: p.name, projectColor: p.color}))
        );
        
        const upcomingTasks = allTasks
            .filter(t => t.dueDate && t.status !== 'done')
            .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))
            .slice(0, 5);

        if (upcomingTasks.length === 0) {
            container.innerHTML = '<p class="empty-state">No upcoming deadlines</p>';
            return;
        }

        container.innerHTML = upcomingTasks.map(task => {
            const isOverdue = new Date(task.dueDate) < new Date();
            const daysUntilDue = Math.ceil((new Date(task.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
            
            return `
                <div class="deadline-item ${isOverdue ? 'overdue' : ''}">
                    <div class="deadline-info">
                        <div class="task-name">${task.name}</div>
                        <div class="project-name" style="color: ${task.projectColor}">${task.projectName}</div>
                    </div>
                    <div class="deadline-date">
                        ${isOverdue ? 
                            `<span class="overdue-text">Overdue by ${Math.abs(daysUntilDue)} days</span>` :
                            `<span class="due-text">${daysUntilDue === 0 ? 'Due today' : `${daysUntilDue} days left`}</span>`
                        }
                    </div>
                </div>
            `;
        }).join('');
    }

    loadGanttChart() {
        const container = document.getElementById('ganttChart');
        if (!this.currentProject || this.currentProject.tasks.length === 0) {
            container.innerHTML = '<p class="empty-state">No tasks to display in Gantt chart</p>';
            return;
        }

        // Create Gantt chart SVG
        const svg = this.createGanttSVG(this.currentProject.tasks);
        container.innerHTML = svg;
    }

    createGanttSVG(tasks) {
        const width = 800;
        const height = tasks.length * 40 + 60;
        const taskHeight = 30;
        const spacing = 40;
        
        // Find date range
        const dates = tasks.flatMap(t => [t.startDate, t.dueDate].filter(Boolean));
        if (dates.length === 0) return '<p class="empty-state">No dates set for tasks</p>';
        
        const minDate = new Date(Math.min(...dates.map(d => new Date(d))));
        const maxDate = new Date(Math.max(...dates.map(d => new Date(d))));
        const dateRange = maxDate - minDate;
        
        let svg = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">`;
        
        // Draw timeline
        svg += `<line x1="200" y1="30" x2="${width - 50}" y2="30" stroke="#ecf0f1" stroke-width="2"/>`;
        
        tasks.forEach((task, index) => {
            const y = index * spacing + 50;
            
            if (task.startDate && task.dueDate) {
                const startX = 200 + ((new Date(task.startDate) - minDate) / dateRange) * (width - 300);
                const endX = 200 + ((new Date(task.dueDate) - minDate) / dateRange) * (width - 300);
                const barWidth = endX - startX;
                
                // Task bar
                svg += `
                    <rect x="${startX}" y="${y}" width="${barWidth}" height="${taskHeight}" 
                          fill="${this.getStatusColor(task.status)}" rx="4" opacity="0.8"/>
                    <text x="10" y="${y + 20}" font-size="12" fill="#2c3e50">${task.name}</text>
                `;
            } else {
                svg += `<text x="10" y="${y + 20}" font-size="12" fill="#95a5a6">${task.name} (No dates)</text>`;
            }
        });
        
        svg += '</svg>';
        return svg;
    }

    getStatusColor(status) {
        const colors = {
            'todo': '#95a5a6',
            'in-progress': '#3498db',
            'review': '#f39c12',
            'done': '#27ae60'
        };
        return colors[status] || '#95a5a6';
    }

    loadTaskCalendar() {
        const container = document.getElementById('taskCalendar');
        container.innerHTML = '<p class="empty-state">Task calendar view - Coming soon!</p>';
    }

    loadTaskList() {
        const container = document.getElementById('taskList');
        if (!this.currentProject || this.currentProject.tasks.length === 0) {
            container.innerHTML = '<p class="empty-state">No tasks in this project</p>';
            return;
        }

        container.innerHTML = `
            <div class="task-list-header">
                <div class="task-list-row">
                    <div class="task-col-name">Task</div>
                    <div class="task-col-status">Status</div>
                    <div class="task-col-priority">Priority</div>
                    <div class="task-col-assignee">Assignee</div>
                    <div class="task-col-due">Due Date</div>
                </div>
            </div>
            <div class="task-list-body">
                ${this.currentProject.tasks.map(task => `
                    <div class="task-list-row">
                        <div class="task-col-name">
                            <div class="task-name">${task.name}</div>
                            <div class="task-description">${task.description || ''}</div>
                        </div>
                        <div class="task-col-status">
                            <span class="status-badge ${task.status}">${task.status}</span>
                        </div>
                        <div class="task-col-priority">
                            <span class="priority-badge ${task.priority}">${task.priority}</span>
                        </div>
                        <div class="task-col-assignee">${task.assignee || '-'}</div>
                        <div class="task-col-due">${task.dueDate ? this.formatDate(task.dueDate) : '-'}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    loadCalendar() {
        const container = document.getElementById('mainCalendar');
        container.innerHTML = '<p class="empty-state">Main calendar view - Coming soon!</p>';
    }

    loadAnalytics() {
        const container = document.querySelector('.analytics-content');
        // Placeholder for analytics
        document.getElementById('projectProgressChart').innerHTML = '<p class="empty-state">Analytics charts - Coming soon!</p>';
        document.getElementById('taskDistributionChart').innerHTML = '<p class="empty-state">Task distribution - Coming soon!</p>';
        document.getElementById('timelineChart').innerHTML = '<p class="empty-state">Timeline chart - Coming soon!</p>';
        document.getElementById('performanceMetrics').innerHTML = '<p class="empty-state">Performance metrics - Coming soon!</p>';
    }

    filterProjects(searchTerm) {
        const filteredProjects = this.projects.filter(project =>
            project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            project.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
        this.displayFilteredProjects(filteredProjects);
    }

    filterProjectsByStatus(status) {
        const filteredProjects = status === 'all' ? 
            this.projects : 
            this.projects.filter(project => project.status === status);
        this.displayFilteredProjects(filteredProjects);
    }

    displayFilteredProjects(projects) {
        const container = document.getElementById('projectsContainer');
        if (projects.length === 0) {
            container.innerHTML = '<p class="empty-state">No projects match your criteria</p>';
            return;
        }
        // Use the same rendering logic as loadProjects but with filtered data
        const originalProjects = this.projects;
        this.projects = projects;
        this.loadProjects();
        this.projects = originalProjects;
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric', 
            year: 'numeric' 
        });
    }

    saveProjects() {
        localStorage.setItem('projects', JSON.stringify(this.projects));
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize the application
const projectManager = new ProjectManager();

// Add some sample data if no projects exist
if (projectManager.projects.length === 0) {
    // Add sample project for demonstration
    const sampleProject = {
        id: 'sample-1',
        name: 'Website Redesign',
        description: 'Complete redesign of company website with modern UI/UX',
        startDate: '2024-01-01',
        endDate: '2024-03-31',
        priority: 'high',
        color: '#3498db',
        status: 'active',
        progress: 45,
        tasks: [
            {
                id: 'task-1',
                name: 'Design Mockups',
                description: 'Create initial design mockups for all pages',
                priority: 'high',
                status: 'done',
                startDate: '2024-01-01',
                dueDate: '2024-01-15',
                assignee: 'John Doe',
                createdAt: '2024-01-01T00:00:00.000Z',
                completedAt: '2024-01-14T00:00:00.000Z'
            },
            {
                id: 'task-2',
                name: 'Frontend Development',
                description: 'Implement responsive frontend using React',
                priority: 'high',
                status: 'in-progress',
                startDate: '2024-01-16',
                dueDate: '2024-02-28',
                assignee: 'Jane Smith',
                createdAt: '2024-01-16T00:00:00.000Z',
                completedAt: null
            },
            {
                id: 'task-3',
                name: 'Content Migration',
                description: 'Migrate existing content to new structure',
                priority: 'medium',
                status: 'todo',
                startDate: '2024-02-01',
                dueDate: '2024-02-15',
                assignee: 'Bob Johnson',
                createdAt: '2024-01-20T00:00:00.000Z',
                completedAt: null
            },
            {
                id: 'task-4',
                name: 'Testing & QA',
                description: 'Comprehensive testing across all devices',
                priority: 'high',
                status: 'todo',
                startDate: '2024-03-01',
                dueDate: '2024-03-15',
                assignee: 'Alice Wilson',
                createdAt: '2024-01-25T00:00:00.000Z',
                completedAt: null
            }
        ],
        createdAt: '2024-01-01T00:00:00.000Z'
    };
    
    projectManager.projects.push(sampleProject);
    projectManager.saveProjects();
    projectManager.updateStats();
}
