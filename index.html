<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProjectFlow - Visual Project Management</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-project-diagram"></i>
                <span>ProjectFlow</span>
            </div>
            <nav class="nav-menu">
                <button class="nav-btn active" data-view="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </button>
                <button class="nav-btn" data-view="projects">
                    <i class="fas fa-folder-open"></i>
                    Projects
                </button>
                <button class="nav-btn" data-view="calendar">
                    <i class="fas fa-calendar-alt"></i>
                    Calendar
                </button>
                <button class="nav-btn" data-view="analytics">
                    <i class="fas fa-chart-line"></i>
                    Analytics
                </button>
            </nav>
            <div class="header-actions">
                <button class="btn-primary" id="newProjectBtn">
                    <i class="fas fa-plus"></i>
                    New Project
                </button>
                <div class="user-profile">
                    <i class="fas fa-user-circle"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Dashboard View -->
        <section id="dashboard-view" class="view active">
            <div class="dashboard-header">
                <h1>Dashboard Overview</h1>
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number" id="totalProjects">0</span>
                            <span class="stat-label">Total Projects</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number" id="totalTasks">0</span>
                            <span class="stat-label">Active Tasks</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number" id="overdueTasks">0</span>
                            <span class="stat-label">Overdue</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number" id="completedTasks">0</span>
                            <span class="stat-label">Completed</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-content">
                <div class="dashboard-left">
                    <div class="recent-projects">
                        <h2>Recent Projects</h2>
                        <div id="recentProjectsList" class="projects-list">
                            <!-- Recent projects will be populated here -->
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-right">
                    <div class="progress-overview">
                        <h2>Progress Overview</h2>
                        <div id="progressChart" class="chart-container">
                            <!-- SVG Progress Chart will be inserted here -->
                        </div>
                    </div>
                    
                    <div class="upcoming-deadlines">
                        <h2>Upcoming Deadlines</h2>
                        <div id="deadlinesList" class="deadlines-list">
                            <!-- Upcoming deadlines will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects View -->
        <section id="projects-view" class="view">
            <div class="projects-header">
                <h1>Projects</h1>
                <div class="view-controls">
                    <div class="view-switcher">
                        <button class="view-btn active" data-project-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-project-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                    <div class="filter-controls">
                        <select id="statusFilter">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                            <option value="on-hold">On Hold</option>
                        </select>
                        <input type="text" id="searchProjects" placeholder="Search projects...">
                    </div>
                </div>
            </div>
            
            <div id="projectsContainer" class="projects-container grid-view">
                <!-- Projects will be populated here -->
            </div>
        </section>

        <!-- Single Project View -->
        <section id="project-detail-view" class="view">
            <div class="project-header">
                <div class="project-title-section">
                    <button class="back-btn" id="backToProjects">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 id="projectTitle">Project Name</h1>
                    <div class="project-status">
                        <span id="projectStatus" class="status-badge">Active</span>
                    </div>
                </div>
                
                <div class="project-controls">
                    <div class="view-switcher">
                        <button class="view-btn active" data-task-view="kanban">
                            <i class="fas fa-columns"></i>
                            Kanban
                        </button>
                        <button class="view-btn" data-task-view="gantt">
                            <i class="fas fa-chart-gantt"></i>
                            Gantt
                        </button>
                        <button class="view-btn" data-task-view="calendar">
                            <i class="fas fa-calendar"></i>
                            Calendar
                        </button>
                        <button class="view-btn" data-task-view="list">
                            <i class="fas fa-list"></i>
                            List
                        </button>
                    </div>
                    <button class="btn-primary" id="newTaskBtn">
                        <i class="fas fa-plus"></i>
                        Add Task
                    </button>
                </div>
            </div>
            
            <div class="project-content">
                <!-- Kanban View -->
                <div id="kanban-view" class="task-view active">
                    <div class="kanban-board">
                        <div class="kanban-column" data-status="todo">
                            <div class="column-header">
                                <h3>To Do</h3>
                                <span class="task-count">0</span>
                            </div>
                            <div class="column-content" id="todoColumn">
                                <!-- Tasks will be populated here -->
                            </div>
                        </div>
                        
                        <div class="kanban-column" data-status="in-progress">
                            <div class="column-header">
                                <h3>In Progress</h3>
                                <span class="task-count">0</span>
                            </div>
                            <div class="column-content" id="inProgressColumn">
                                <!-- Tasks will be populated here -->
                            </div>
                        </div>
                        
                        <div class="kanban-column" data-status="review">
                            <div class="column-header">
                                <h3>Review</h3>
                                <span class="task-count">0</span>
                            </div>
                            <div class="column-content" id="reviewColumn">
                                <!-- Tasks will be populated here -->
                            </div>
                        </div>
                        
                        <div class="kanban-column" data-status="done">
                            <div class="column-header">
                                <h3>Done</h3>
                                <span class="task-count">0</span>
                            </div>
                            <div class="column-content" id="doneColumn">
                                <!-- Tasks will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Gantt View -->
                <div id="gantt-view" class="task-view">
                    <div class="gantt-container">
                        <div id="ganttChart" class="gantt-chart">
                            <!-- Gantt chart will be rendered here -->
                        </div>
                    </div>
                </div>
                
                <!-- Calendar View -->
                <div id="calendar-view" class="task-view">
                    <div class="calendar-container">
                        <div id="taskCalendar" class="task-calendar">
                            <!-- Calendar will be rendered here -->
                        </div>
                    </div>
                </div>
                
                <!-- List View -->
                <div id="list-view" class="task-view">
                    <div class="task-list-container">
                        <div id="taskList" class="task-list">
                            <!-- Task list will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Calendar View -->
        <section id="calendar-view-main" class="view">
            <div class="calendar-header">
                <h1>Calendar</h1>
                <div class="calendar-controls">
                    <button class="btn-secondary" id="prevMonth">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="currentMonth">January 2024</span>
                    <button class="btn-secondary" id="nextMonth">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            <div id="mainCalendar" class="main-calendar">
                <!-- Main calendar will be rendered here -->
            </div>
        </section>

        <!-- Analytics View -->
        <section id="analytics-view" class="view">
            <div class="analytics-header">
                <h1>Analytics & Reports</h1>
            </div>
            <div class="analytics-content">
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Project Progress</h3>
                        <div id="projectProgressChart" class="chart-container">
                            <!-- Project progress chart -->
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Task Distribution</h3>
                        <div id="taskDistributionChart" class="chart-container">
                            <!-- Task distribution chart -->
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Timeline Overview</h3>
                        <div id="timelineChart" class="chart-container">
                            <!-- Timeline chart -->
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Performance Metrics</h3>
                        <div id="performanceMetrics" class="metrics-container">
                            <!-- Performance metrics -->
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <!-- New Project Modal -->
    <div id="newProjectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Create New Project</h2>
                <button class="close-btn" data-modal="newProjectModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="newProjectForm">
                    <div class="form-group">
                        <label for="projectName">Project Name</label>
                        <input type="text" id="projectName" required>
                    </div>
                    <div class="form-group">
                        <label for="projectDescription">Description</label>
                        <textarea id="projectDescription" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="projectStartDate">Start Date</label>
                            <input type="date" id="projectStartDate" required>
                        </div>
                        <div class="form-group">
                            <label for="projectEndDate">End Date</label>
                            <input type="date" id="projectEndDate" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="projectPriority">Priority</label>
                        <select id="projectPriority">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="projectColor">Project Color</label>
                        <div class="color-picker">
                            <input type="color" id="projectColor" value="#3498db">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" data-modal="newProjectModal">Cancel</button>
                <button type="submit" form="newProjectForm" class="btn-primary">Create Project</button>
            </div>
        </div>
    </div>

    <!-- New Task Modal -->
    <div id="newTaskModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Create New Task</h2>
                <button class="close-btn" data-modal="newTaskModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="newTaskForm">
                    <div class="form-group">
                        <label for="taskName">Task Name</label>
                        <input type="text" id="taskName" required>
                    </div>
                    <div class="form-group">
                        <label for="taskDescription">Description</label>
                        <textarea id="taskDescription" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskPriority">Priority</label>
                            <select id="taskPriority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="taskStatus">Status</label>
                            <select id="taskStatus">
                                <option value="todo" selected>To Do</option>
                                <option value="in-progress">In Progress</option>
                                <option value="review">Review</option>
                                <option value="done">Done</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskStartDate">Start Date</label>
                            <input type="date" id="taskStartDate">
                        </div>
                        <div class="form-group">
                            <label for="taskDueDate">Due Date</label>
                            <input type="date" id="taskDueDate">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="taskAssignee">Assignee</label>
                        <input type="text" id="taskAssignee" placeholder="Enter assignee name">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" data-modal="newTaskModal">Cancel</button>
                <button type="submit" form="newTaskForm" class="btn-primary">Create Task</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
</body>
</html>
